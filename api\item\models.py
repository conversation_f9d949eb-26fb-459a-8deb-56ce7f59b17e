from django.db import models
from django_jsonform.models.fields import <PERSON><PERSON><PERSON><PERSON>
from django.template import defaultfilters
from storages.backends.s3boto3 import S3Boto3Storage  

class ItemListModel(models.Model):
    ITEMS_SCHEMA_FILES = {
        'type' : 'array',
        'items' : {
            'type' : 'object',
            'keys' : {
                'name' : {
                    'type' : 'string'
                },
                'url' : {
                    'type' : 'string'
                }
            }
        }
    }
    item_id = models.CharField(max_length=255, blank=True, null=True, default='')
    title = models.CharField(max_length=255, blank=True, null=True, default='')
    description = models.CharField(max_length=255, blank=True, null=True, default='')
    thumbnail = JSONField(schema=ITEMS_SCHEMA_FILES, null=True)
    banner_image = models.CharField(max_length=255, blank=True, null=True, default='')
    category_name = models.CharField(max_length=500, blank=True, null=True, default='')
    created_at = models.DateTimeField(auto_now_add=True)
  
    class Meta: 
        ordering = ('-created_at',)

    def created_at_formatted(self):
        return defaultfilters.date(self.created_at, 'M d, Y')

class FileUploadModel(models.Model):
    file = models.FileField(storage=S3Boto3Storage(), upload_to='fabricsplus/items/uploads/')
    uploaded_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.file.name