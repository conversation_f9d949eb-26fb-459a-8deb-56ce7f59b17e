// generated by the @nuxtjs/tailwindcss <https://github.com/nuxt-modules/tailwindcss> module at 9/25/2025, 12:24:57 AM
import "@nuxtjs/tailwindcss/config-ctx"
import configMerger from "@nuxtjs/tailwindcss/merger";

import cfg2 from "./../../tailwind.config.js";
const config = [
{"content":{"files":["C:/Users/<USER>/OneDrive/Desktop/fabricsplus/frontend/components/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Desktop/fabricsplus/frontend/components/global/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Desktop/fabricsplus/frontend/components/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Desktop/fabricsplus/frontend/layouts/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Desktop/fabricsplus/frontend/plugins/**/*.{js,ts,mjs}","C:/Users/<USER>/OneDrive/Desktop/fabricsplus/frontend/composables/**/*.{js,ts,mjs}","C:/Users/<USER>/OneDrive/Desktop/fabricsplus/frontend/utils/**/*.{js,ts,mjs}","C:/Users/<USER>/OneDrive/Desktop/fabricsplus/frontend/pages/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Desktop/fabricsplus/frontend/{A,a}pp.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Desktop/fabricsplus/frontend/{E,e}rror.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/OneDrive/Desktop/fabricsplus/frontend/app.config.{js,ts,mjs}"]}},
{},
cfg2
].reduce((acc, curr) => configMerger(acc, curr), {});

const resolvedConfig = config;

export default resolvedConfig;