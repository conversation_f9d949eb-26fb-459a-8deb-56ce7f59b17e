<template>
  <div class="bg-white text-black">
    <!-- Header -->
    <header
      class="sticky top-0 z-40 backdrop-blur-sm bg-gradient-to-r from-[#ddd2c2]/90 via-[#ebeae4]/90 to-[#f5e6ca]/90">
      <div class="max-w-7xl mx-auto px-6 py-3 flex items-center justify-between text-[#2c2c2c] relative">
        <!-- Branding -->
        <NuxtLink to="/">
          <div class="flex items-center gap-2 text-neutral-900">
            <img src="/HDLogoFabricsPlusV2.png" class="w-10" />
            <div>
              <p class="text-lg font-bold">
                Fabrics <span class="text-red-700">Plus</span>
              </p>
              <p class="text-xs text-gray-800">Philippines</p>
            </div>
          </div>
        </NuxtLink>

        <!-- Desktop Nav -->
        <nav class="hidden sm:flex gap-6 text-sm font-medium uppercase text-[#2c2c2c]/90">
          <NuxtLink to="/about" class="nav-link hover:text-red-700">About</NuxtLink>
          <NuxtLink to="/contact" class="nav-link  text-red-700">Contact</NuxtLink>
          <NuxtLink to="/showroom/curtains" class="nav-link hover:text-red-700">Showroom</NuxtLink>
        </nav>

        <!-- Call Button (Desktop) -->
        <a :href="`tel:${'0917-323-1366'}`"
          class="hidden sm:inline-flex items-center gap-2 rounded-full px-4 py-2 border border-red-700 text-red-700 font-semibold hover:bg-red-700 hover:text-[#f5f5f2] transition">
          📞 Call Us
        </a>

        <!-- Mobile Burger -->
        <div class="relative sm:hidden">
          <button @click="isOpen = !isOpen" class="text-3xl text-red-800 font-bold">
            ☰
          </button>

          <!-- Floating Dropdown -->
          <div v-if="isOpen"
            class="absolute right-0 mt-2 w-48 bg-[#f5e6ca] border border-red-700/30 rounded-lg shadow-lg py-3 space-y-2 text-[#2c2c2c] font-medium uppercase">
            <NuxtLink to="/about" class="block px-4 py-2 hover:bg-red-700 hover:text-[#f5f5f2] rounded-md">About
            </NuxtLink>
            <NuxtLink to="/contact" class="block px-4 py-2 hover:bg-red-700 hover:text-[#f5f5f2] rounded-md">Contact
            </NuxtLink>
            <NuxtLink to="/showroom/curtains" class="block px-4 py-2 hover:bg-red-700 hover:text-[#f5f5f2] rounded-md">Showroom
            </NuxtLink>
            <a :href="`tel:${'0917-323-1366'}`"
              class="block px-4 py-2 hover:bg-red-700 hover:text-[#f5f5f2] rounded-md">📞 Call
              Us</a>
          </div>
        </div>
      </div>
    </header>

    <!-- Hero -->
    <section v-reveal="'float-xs'" class="relative overflow-hidden">
      <div class="absolute inset-0">
        <img src="https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/improved/withTM2.png"
          alt="Contact Fabrics Plus" class="h-full w-full object-cover" />
        <div class="absolute inset-0 bg-black/60"></div>
      </div>
      <div class="relative z-10 mx-auto max-w-3xl px-6 py-24 text-center text-white">
        <h2 class="text-3xl sm:text-5xl font-bold mb-4">Get in Touch</h2>
        <p class="text-base sm:text-lg max-w-xl mx-auto leading-relaxed">
          Have a question, need assistance, or want to book a consultation?
          Reach out to Fabrics Plus and we’ll be glad to help.
        </p>
      </div>
    </section>

    <!-- Contact Section -->
    <section v-reveal="'float-lg'" class="bg-gradient-to-br from-rose-50 via-white to-indigo-50 py-16">
      <div class="max-w-6xl mx-auto px-4">
        <!-- ✅ Force equal height columns -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-stretch">

          <!-- ✅ Toast Notification -->
          <transition enter-active-class="transform transition duration-300 ease-out"
            enter-from-class="opacity-0 scale-95" enter-to-class="opacity-100 scale-100"
            leave-active-class="transform transition duration-200 ease-in" leave-from-class="opacity-100 scale-100"
            leave-to-class="opacity-0 scale-95">
            <div v-if="showToast" class="absolute inset-0 flex items-center justify-center z-50"
              style="position: absolute; top: 0; left: 0; right: 0; bottom: 0;">
              <div class="px-6 py-4 rounded-2xl shadow-xl text-sm font-medium text-center
              backdrop-blur-md border border-red-700/20 max-w-xs" :class="success
                ? 'bg-gradient-to-r from-red-600/90 to-red-500/90 text-white'
                : 'bg-gradient-to-r from-gray-800/90 to-gray-700/90 text-red-100'">
                {{ success || error }}
              </div>
            </div>
          </transition>

          <!-- ✅ Contact Form (now equal height) -->
          <form class="relative bg-white p-6 sm:p-8 rounded-2xl shadow-lg space-y-6 w-full flex flex-col h-full"
            @submit.prevent="handleSubmit">

            <!-- Fields -->
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div class="relative">
                <input type="text" required
                  class="peer w-full px-3 pt-5 pb-2 text-sm border rounded-md focus:ring-2 focus:ring-red-800 outline-none"
                  v-model="contacts.firstname" />
                <label class="absolute left-3 top-2 text-gray-400 text-xs">First Name</label>
              </div>
              <div class="relative">
                <input type="text" required
                  class="peer w-full px-3 pt-5 pb-2 text-sm border rounded-md focus:ring-2 focus:ring-red-800 outline-none"
                  v-model="contacts.lastname" />
                <label class="absolute left-3 top-2 text-gray-400 text-xs">Last Name</label>
              </div>
            </div>

            <div class="relative">
              <input type="email" required
                class="peer w-full px-3 pt-5 pb-2 text-sm border rounded-md focus:ring-2 focus:ring-red-800 outline-none"
                v-model="contacts.contact_email" />
              <label class="absolute left-3 top-2 text-gray-400 text-xs">Email</label>
            </div>

            <div class="relative">
              <input type="text"
                class="peer w-full px-3 pt-5 pb-2 text-sm border rounded-md focus:ring-2 focus:ring-red-800 outline-none"
                v-model="contacts.contact_number" />
              <label class="absolute left-3 top-2 text-gray-400 text-xs">Contact Number</label>
            </div>

            <div class="relative flex-1">
              <textarea required
                class="peer w-full px-3 pt-5 pb-2 text-sm border rounded-md h-36 focus:ring-2 focus:ring-red-800 outline-none"
                v-model="contacts.message" placeholder="Complete your inquiry details."></textarea>
              <label class="absolute left-3 top-2 text-gray-400 text-xs">Message</label>
            </div>

            <!-- Button stays pinned at bottom -->
            <div>
              <button type="submit"
                class="bg-red-900 text-white px-6 py-3 rounded-lg hover:bg-black w-full font-medium transition"
                :disabled="loading">
                {{ loading ? "Sending..." : "Send Message" }}
              </button>
            </div>
          </form>

          <!-- ✅ Contact Info + Map -->
          <div class="space-y-6 flex flex-col h-full">
            <!-- Info Card -->
            <div class="bg-white p-6 sm:p-8 rounded-2xl shadow-lg text-sm space-y-4 flex-1">
              <h3 class="text-xl font-semibold text-gray-900 mb-2">Visit Us</h3>
              <p class="text-gray-700">
                For over 30 years, Fabrics Plus has been Cebu’s trusted source
                for curtains, blinds, bedsheets, chair covers, and premium upholstery supplies.
              </p>
              <div class="grid gap-3 text-gray-800">
                <p><span class="font-bold">📍 Address:</span> SM City Cebu</p>
                <p><span class="font-bold">📞 Phone:</span> +63 ************</p>
                <p><span class="font-bold">✉ Email:</span> <EMAIL></p>
                <p><span class="font-bold">🌐 Website:</span> fabricspluscurtains.com</p>
              </div>
            </div>

            <!-- Map -->
            <div class="rounded-2xl shadow-md overflow-hidden flex-1 min-h-[280px]">
              <iframe class="w-full h-full"
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d62822.25642328885!2d123.8476!3d10.3157!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x33a9994f25d18e11%3A0xa0df5c39c3c44c9d!2sSM%20City%20Cebu!5e0!3m2!1sen!2sph!4v1692712345678"
                style="border:0;" allowfullscreen="" loading="lazy">
              </iframe>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-neutral-900 text-[#f5f5f2] py-10 mt-10">
      <div class="max-w-7xl mx-auto px-6 grid grid-cols-1 sm:grid-cols-3 gap-8">
        <!-- Brand -->
        <div>
          <img src="https://fpstorage.sgp1.cdn.digitaloceanspaces.com/HDLogoFabricsPlus.png" class="w-12 mb-3"
            alt="Logo" />
          <p class="font-bold">Cebu Las Aguadas Ventures Corp.</p>
          <p class="text-sm">© 2025 Fabrics Plus. All Rights Reserved.</p>
        </div>

        <!-- Quick Links -->
        <div>
          <h3 class="font-semibold mb-3">Quick Links</h3>
          <ul class="space-y-2">
            <li>
              <NuxtLink to="/about" class="nav-link hover:text-red-500">About</NuxtLink>
            </li>
            <li>
              <NuxtLink to="/comingsoon" class="nav-link hover:text-red-500">Contact</NuxtLink>
            </li>
            <li>
              <NuxtLink to="/showroom/curtains" class="nav-link hover:text-red-500">Showroom</NuxtLink>
            </li>
          </ul>
        </div>

        <!-- Socials -->
        <div>
          <h3 class="font-semibold mb-3">Follow Us</h3>
          <div class="flex gap-6 items-center">
            <!-- Facebook -->
            <a href="https://facebook.com/fabricspluscebu" target="_blank" aria-label="Facebook"
              class="text-gray-500 hover:text-red-800 transition">
              <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24" class="w-8 h-8">
                <path
                  d="M22 12a10 10 0 1 0-11.5 9.9v-7H8v-3h2.5V9.5a3.5 3.5 0 0 1 3.7-3.9c1 0 2 .2 2 .2v2.3H15a2 2 0 0 0-2.3 2V12H16l-.5 3h-2v7A10 10 0 0 0 22 12" />
              </svg>
            </a>

            <!-- Instagram -->
            <a href="https://instagram.com/fabricspluscebu" target="_blank" aria-label="Instagram"
              class="text-gray-500 hover:text-red-800 transition">
              <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24" class="w-8 h-8">
                <path
                  d="M7 2C4.2 2 2 4.2 2 7v10c0 2.8 2.2 5 5 5h10c2.8 0 5-2.2 5-5V7c0-2.8-2.2-5-5-5H7zm10 2c1.7 0 3 1.3 3 3v10c0 1.7-1.3 3-3 3H7c-1.7 0-3-1.3-3-3V7c0-1.7 1.3-3 3-3h10zm-5 3a5 5 0 1 0 0 10 5 5 0 0 0 0-10zm0 2a3 3 0 1 1 0 6 3 3 0 0 1 0-6zm4.8-.9a1.1 1.1 0 1 0 0-2.2 1.1 1.1 0 0 0 0 2.2z" />
              </svg>
            </a>

            <!-- YouTube -->
            <a href="https://youtube.com/@fabricspluscebu" target="_blank" aria-label="YouTube"
              class="text-gray-500 hover:text-red-800 transition">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-8 h-8">
                <path
                  d="M23.5 6.2a3 3 0 0 0-2.1-2.1C19.5 3.5 12 3.5 12 3.5s-7.5 0-9.4.6a3 3 0 0 0-2.1 2.1C0 8.1 0 12 0 12s0 3.9.5 5.8a3 3 0 0 0 2.1 2.1c1.9.6 9.4.6 9.4.6s7.5 0 9.4-.6a3 3 0 0 0 2.1-2.1c.5-1.9.5-5.8.5-5.8s0-3.9-.5-5.8zM9.8 15.5v-7l6.2 3.5-6.2 3.5z" />
              </svg>
            </a>


            <!-- TikTok -->
            <a href="https://tiktok.com/@fabricspluscebu" target="_blank" aria-label="TikTok"
              class="text-gray-500 hover:text-red-800 transition">
              <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24" class="w-8 h-8">
                <path
                  d="M12.7 2h3c.1 1 .5 2 1 ******* 1.6 1.6 2.7 1.8v3a6.4 6.4 0 0 1-3.7-1.2v7.6a6.4 6.4 0 1 1-6.4-6.4c.4 0 .7 0 1 .1v3a3.3 3.3 0 1 0 2.4 3.2V2z" />
              </svg>
            </a>
          </div>
        </div>

      </div>
    </footer>
  </div>
</template>
<script setup>
import { ref } from 'vue'
import moment from 'moment'

const contacts = ref({
  contact_id: "CID" + moment().valueOf(),
  firstname: '',
  lastname: '',
  contact_email: '',
  contact_number: '',
  message: '',
})

const loading = ref(false)
const success = ref(null)
const error = ref(null)
const showToast = ref(false)

const handleSubmit = async () => {
  loading.value = true
  success.value = null
  error.value = null

  try {
    // Step 1: Save contact to database
    await $fetch('https://api.fabricspluscurtains.com/api/contact/create/', {
      method: 'POST',
      body: contacts.value,
    })

    // Step 2: Send to Gmail API
    await sendToGmail()

    success.value = '✅ Your message has been sent successfully!'
    showToast.value = true

    // Reset form
    contacts.value = {
      contact_id: "CID" + moment().valueOf(),
      firstname: '',
      lastname: '',
      contact_email: '',
      contact_number: '',
      message: '',
    }

    // Auto-hide toast after 3s
    setTimeout(() => {
      showToast.value = false
    }, 3000)

  } catch (err) {
    console.error('Error submitting contact:', err)
    error.value = '⚠️ Failed to send message. Please try again.'
    showToast.value = true

    // Auto-hide toast after 3s
    setTimeout(() => {
      showToast.value = false
    }, 3000)
  } finally {
    loading.value = false
  }
}

const sendToGmail = async () => {
  try {
    await $fetch('https://api.fabricspluscurtains.com/api/contact/gmail-notif/', {
      method: 'POST',
      body: contacts.value,
    })
    console.log('Gmail notification sent!')
  } catch (err) {
    console.error('Error sending Gmail notification:', err)
    throw err // bubble up error to handleSubmit
  }
}
</script>
<style scoped>
.nav-link {
  @apply relative transition;
}

.nav-link::after {
  content: "";
  @apply absolute bottom-0 left-0 w-0 h-0.5 bg-red-600 transition-all;
}

.nav-link:hover::after {
  @apply w-full;
}

/* ✅ Toast animation */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
