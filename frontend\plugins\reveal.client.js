export default defineNuxtPlugin((nuxtApp) => {
    nuxtApp.vueApp.directive("reveal", {
        mounted(el, binding) {
            const observer = new IntersectionObserver(
                (entries) => {
                    entries.forEach((entry) => {
                        if (entry.isIntersecting) {
                            const val = binding.value || "up-sm";
                            const [mode, speedKey] = val.split("-");

                            const speeds = {
                                xs: "duration-300",
                                sm: "duration-700",
                                md: "duration-[1200ms]",
                                lg: "duration-[2000ms]",
                            };
                            const speed = speeds[speedKey] || speeds.sm;

                            // remove the offset class before animating
                            if (mode === "float") {
                                el.classList.remove("translate-y-16", "translate-y-32");
                            } else {
                                el.classList.remove("translate-y-6");
                            }

                            el.classList.add("opacity-100", "translate-y-0", speed);

                            observer.unobserve(el);
                        }

                    });
                },
                { threshold: 0.6 } // reveal only when ~center of screen
            );

            // Initial hidden state
            el.classList.add("opacity-0", "transition-all", "ease-out");

            // Mode-based starting offset
            const val = binding.value || "up-sm";
            const [mode] = val.split("-");
            if (mode === "float") {
                el.classList.add("translate-y-16"); // smoother float
            } else {
                el.classList.add("translate-y-6"); // default shorter offset
            }

            observer.observe(el);
        },
    });
});
