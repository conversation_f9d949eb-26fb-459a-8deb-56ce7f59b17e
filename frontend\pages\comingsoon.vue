<template>
    <div class="min-h-screen text-[#2c2c2c] bg-gradient-to-br from-[#ddd2c2] via-[#ebeae4] to-[#f5e6ca] flex flex-col">
        <!-- Header -->

        <header class="py-2 px-6 flex justify-center items-center">
            <NuxtLink to="/">
                <div class="flex items-center gap-2">
                    <img src="/HDLogoFabricsPlusV2.png" class="w-10" />
                    <div>
                        <p class="text-lg font-bold">
                            Fabrics <span class="text-red-700">Plus</span>
                        </p>
                        <p class="text-xs text-gray-800">Philippines</p>
                    </div>
                </div>
            </NuxtLink>
        </header>

        <!-- Hero -->
        <main v-reveal="'up-sm'" class="flex-1 flex items-center justify-center relative overflow-hidden">
            <!-- Background Images -->
            <transition-group name="fade" tag="div" class="absolute inset-0">
                <div v-for="(bg, i) in [currentBg]" :key="bg" class="absolute inset-0 bg-cover bg-center"
                    :style="{ backgroundImage: `url(${bg})` }"></div>
            </transition-group>

            <!-- Overlay -->
            <div class="absolute inset-0 bg-black/40"></div>

            <!-- Text -->
            <div v-reveal="'float-lg'" class="relative z-10 text-center text-white px-6">
                <h1 class="text-4xl sm:text-6xl font-bold mb-6">
                    Coming <span class="text-red-600">Soon</span>
                </h1>
                <p class="text-lg sm:text-xl mb-8">
                    We’re crafting something stylish and exciting for your interiors. Stay tuned!
                </p>
            </div>
        </main>

        <!-- Footer -->
        <footer class="bg-neutral-900 text-[#f5f5f2] py-6 text-center">
            <p class="text-sm">© 2025 Fabrics Plus. All Rights Reserved.</p>
        </footer>
    </div>
</template>

<script setup>
import { ref, onMounted } from "vue";

const heroimages = [
    "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/improved/withTM1.png",
    "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/improved/withTM2.png",
    "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/improved/withTM3.png",
    "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/improved/withTM4.png",
];

const currentBg = ref(heroimages[0]);
let bgIndex = 0;

function cycleBackground() {
    setInterval(() => {
        bgIndex = (bgIndex + 1) % heroimages.length;
        currentBg.value = heroimages[bgIndex];
    }, 5000); // every 5s
}

onMounted(() => {
    cycleBackground();
});

</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
    transition: opacity 1s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}
</style>
