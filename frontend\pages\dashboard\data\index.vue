<script setup>
import { ref, onMounted } from 'vue'

const contactsList = ref([])

// Example API endpoint (replace with your API URL)
const apiUrl = 'http://127.0.0.1:8000/api/fabricsplus/contact/list/'

onMounted(async () => {
  try {
    const res = await $fetch(apiUrl)
    contactsList.value = res
  } catch (err) {
    console.error('Error fetching contactsList:', err)
  }
})


console.log(apiUrl)
</script>

<template>
  <div class="p-4">
    <h1 class="text-xl font-bold mb-4">Contact List</h1>

    <ul>
      <li 
        v-for="contact in contactsList" 
        :key="contact.id" 
        class="border p-2 rounded mb-2"
      >
        <h2 class="font-semibold">{{ contact.firstname }}</h2>
        <p>{{ contact.lastname }}</p>
         <p>{{ contact.contact_email }}</p>
          <p>{{ contact.contact_number }}</p>
             <p>{{ contact.message }}</p>
      </li>
    </ul>
  </div>
</template>
