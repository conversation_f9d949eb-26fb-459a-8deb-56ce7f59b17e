<template>
  <div class="min-h-screen bg-gradient-to-br from-[#ddd2c2] via-[#ebeae4] to-[#f5e6ca] text-[#2c2c2c] flex flex-col">

    <!-- Header -->
    <header
      class="sticky top-0 z-40 backdrop-blur-sm bg-gradient-to-r from-[#ddd2c2]/90 via-[#ebeae4]/90 to-[#f5e6ca]/90">
      <div class="max-w-7xl mx-auto px-6 py-3 flex items-center justify-between text-[#2c2c2c] relative">
        <!-- Branding -->
        <NuxtLink to="/">
          <div class="flex items-center gap-2 text-neutral-900">
            <img src="/HDLogoFabricsPlusV2.png" class="w-10" />
            <div>
              <p class="text-lg font-bold">
                Fabrics <span class="text-red-700">Plus</span>
              </p>
              <p class="text-xs text-gray-800">Philippines</p>
            </div>
          </div>
        </NuxtLink>

        <!-- Desktop Nav -->
        <nav class="hidden sm:flex gap-6 text-sm font-medium uppercase text-[#2c2c2c]/90">
          <NuxtLink to="/about" class="nav-link text-red-700">About</NuxtLink>
          <NuxtLink to="/contact" class="nav-link hover:text-red-700">Contact</NuxtLink>
          <NuxtLink to="/showroom/curtains" class="nav-link hover:text-red-700">Showroom</NuxtLink>
        </nav>

        <!-- Call Button (Desktop) -->
        <a :href="`tel:${'0917-323-1366'}`"
          class="hidden sm:inline-flex items-center gap-2 rounded-full px-4 py-2 border border-red-700 text-red-700 font-semibold hover:bg-red-700 hover:text-[#f5f5f2] transition">
          📞 Call Us
        </a>

        <!-- Mobile Burger -->
        <div class="relative sm:hidden">
          <button @click="isOpen = !isOpen" class="text-3xl text-red-800 font-bold">
            ☰
          </button>

          <!-- Floating Dropdown -->
          <div v-if="isOpen"
            class="absolute right-0 mt-2 w-48 bg-[#f5e6ca] border border-red-700/30 rounded-lg shadow-lg py-3 space-y-2 text-[#2c2c2c] font-medium uppercase">
            <NuxtLink to="/about" class="block px-4 py-2 hover:bg-red-700 hover:text-[#f5f5f2] rounded-md">About
            </NuxtLink>
            <NuxtLink to="/contact" class="block px-4 py-2 hover:bg-red-700 hover:text-[#f5f5f2] rounded-md">Contact
            </NuxtLink>
            <NuxtLink to="/showroom/curtains" class="block px-4 py-2 hover:bg-red-700 hover:text-[#f5f5f2] rounded-md">Showroom
            </NuxtLink>
            <a :href="`tel:${'0917-323-1366'}`"
              class="block px-4 py-2 hover:bg-red-700 hover:text-[#f5f5f2] rounded-md">📞 Call
              Us</a>
          </div>
        </div>
      </div>
    </header>

    <!-- Hero -->
    <section v-reveal="'up-lg'" class="relative h-[40vh] flex items-center justify-center overflow-hidden">
      <!-- Backgrounds -->
      <transition name="fade" mode="out-in">
        <div :key="currentBg" class="absolute inset-0 bg-cover bg-center transition-opacity duration-1000"
          :style="{ backgroundImage: `url(${currentBg})` }">
        </div>
      </transition>

      <!-- Overlays -->
      <div class="absolute inset-0 bg-gradient-to-br from-black/60 to-black/90"></div>
      <!-- <div class="absolute inset-0 bg-gradient-to-br from-[#fffaf8]/40 via-[#fffdfc]/70 to-[#fff9f3]/30"></div> -->

      <!-- Content -->
      <div class="relative mx-auto max-w-6xl px-6 py-24 text-center">
        <h1 v-reveal="'float-lg'" class="text-3xl sm:text-5xl font-bold tracking-tight text-[#f5f5f2] mb-10">
          Our
          <span class="text-red-700"> story </span>is woven into every fabric we deliver
        </h1>
        <p v-reveal="'up-md'" class="text-lg text-[#f5f5f2]/80 max-w-4xl mx-auto mb-8">
          At Fabrics Plus, we believe every detail tells a story. For years, we’ve dedicated ourselves to creating
          timeless fabric solutions that transform spaces and reflect personalities.
        </p>
        <!-- Call Buttons -->
        <div class="flex flex-wrap justify-center gap-8">
          <!-- Mobile Number -->
          <a :href="`tel:${'+639173231366'}`"
            class="hidden sm:inline-flex items-center gap-2 rounded-full px-5 py-2 bg-red-700 text-white font-semibold shadow hover:bg-transparent hover:text-red-700 hover:border hover:border-red-700 transition">
            <LucideIcons.Phone class="w-4 h-4" />
            0917-323-1366
          </a>

          <!-- Landline Number -->
          <a :href="`tel:${'063322320740'}`"
            class="hidden sm:inline-flex items-center gap-2 rounded-full px-5 py-2 border border-white/80 text-[#f5f5f2] hover:bg-neutral-800 hover:text-[#f5f5f2] font-semibold transition">
            <LucideIcons.Phone class="w-4 h-4" />
            (06332) 232-0740
          </a>
        </div>
      </div>


    </section>

    <!-- Main Content -->
    <main class="flex-1 bg-transparent py-16">
      <div class="mx-auto max-w-7xl space-y-16">
        <!-- Brochure View -->
        <section v-if="mode === 'brochure'" class="space-y-16">

          <!-- Offers Section -->
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mx-8">
            <article v-reveal="'float-md'" v-for="item in offers" :key="item.title"
              class="relative rounded-2xl overflow-hidden shadow-lg group h-[35vh]">
              <!-- Background Image -->
              <div class="absolute inset-0 bg-cover bg-center" :style="`background-image: url(${item.image});`"></div>

              <!-- Elegant Overlay -->
              <div class="absolute inset-0 bg-gradient-to-b from-[#e8dfd2]/80 via-[#d4c5b5]/90 to-[#cbb6a3]"></div>

              <!-- Content -->
              <div v-reveal="'up-lg'" class="relative h-full flex flex-col items-center text-center p-8">
                <!-- Icon -->
                <component :is="item.icon" class="w-24 h-24 text-red-500 mb-4 shrink-0" />

                <!-- Title + Description -->
                <div class="flex flex-col items-center px-5">
                  <h3 class="font-bold text-xl text-[#2c2c2c] text-center mb-3 tracking-wide">
                    {{ item.title }}
                  </h3>
                  <p class="text-sm text-[#3b3b3b] leading-relaxed text-justify line-clamp-4">
                    {{ item.desc }}
                  </p>
                </div>
              </div>
            </article>
          </div>

        </section>
      </div>

      <!-- Differentiators Ribbon (full width) -->
      <div v-reveal="'up-lg'"
        class="w-full mt-16 bg-gradient-to-r from-[#ebeae4]/80 via-[#f5e6ca]/90 to-[#ebeae4]/70 shadow-lg py-6 px-6">
        <div class="max-w-6xl mx-auto">
          <!-- Title -->
          <h2 v-reveal="'float-sm'" class="text-3xl font-semibold text-center mb-4 text-[#2c2c2c]">
            What Makes Us Different
          </h2>

          <!-- Differentiator Rows -->
          <div v-reveal="'float-lg'" class="grid grid-cols-1 md:grid-cols-3 gap-10 justify-items-center">
            <div v-for="(diff, i) in differentiators" :key="i" class="flex items-stretch max-w-md w-full">
              <!-- Big Number with Divider -->
              <div class="flex items-center">
                <span
                  class="text-[8rem] w-24 leading-none font-extrabold text-red-800/70 pr-6 border-r-4 border-red-800/50">
                  {{ i + 1 }}
                </span>
              </div>

              <!-- Content -->
              <div class="flex flex-col justify-center pl-6">
                <h3 class="font-semibold text-xl mb-3 tracking-wide text-red-800/70">
                  {{ diff.title }}
                </h3>
                <p class="text-sm leading-relaxed text-[#3b3b3b]">
                  {{ diff.text }}
                </p>
              </div>
            </div>
          </div>

        </div>
      </div>

      <!-- Promise (enhanced for emphasis) -->
      <div v-reveal="'float-sm'" class="mx-auto px-4 max-w-7xl mt-16">

        <div class="relative rounded-2xl overflow-hidden shadow-lg py-16">
          <transition name="fade" mode="out-in">
            <div :key="currentBg"
              class="absolute inset-0 bg-cover bg-center transition-opacity duration-2000 rounded-2xl "
              :style="{ backgroundImage: `url(${currentBg})` }">
            </div>
          </transition>
          <!-- Background Gradient -->
          <div class="absolute inset-0 bg-gradient-to-r from-[#ddd2c2] via-[#ffe8e0] to-[#ddd2c2] opacity-90"></div>

          <!-- Content -->
          <div class="relative p-12 text-center">
            <h2 class="text-5xl md:text-6xl font-extrabold text-[#2c2c2c] mb-5">
              Our <span class="text-red-700">Promise</span>
            </h2>
            <p class="text-lg md:text-xl text-[#3b3b3b] leading-relaxed max-w-2xl mx-auto">
              Every stitch, every fold, every finish — designed with precision. We’re committed to turning your vision
              into reality with fabric solutions that blend beauty, function, and lasting quality.
            </p>
          </div>
        </div>
      </div>

    </main>


    <!-- Footer -->
    <footer class="bg-neutral-900 text-[#f5f5f2] py-10 mt-10">
      <div class="max-w-7xl mx-auto px-6 grid grid-cols-1 sm:grid-cols-3 gap-8">
        <!-- Brand -->
        <div>
          <img src="https://fpstorage.sgp1.cdn.digitaloceanspaces.com/HDLogoFabricsPlus.png" class="w-12 mb-3"
            alt="Logo" />
          <p class="font-bold">Cebu Las Aguadas Ventures Corp.</p>
          <p class="text-sm">© 2025 Fabrics Plus. All Rights Reserved.</p>
        </div>

        <!-- Quick Links -->
        <div>
          <h3 class="font-semibold mb-3">Quick Links</h3>
          <ul class="space-y-2">
            <li>
              <NuxtLink to="/about" class="nav-link hover:text-red-500">About</NuxtLink>
            </li>
            <li>
              <NuxtLink to="/contact" class="nav-link hover:text-red-500">Contact</NuxtLink>
            </li>
            <li>
              <NuxtLink to="/showroom/curtains" class="nav-link hover:text-red-500">Showroom</NuxtLink>
            </li>
          </ul>
        </div>

        <!-- Socials -->
        <div>
          <h3 class="font-semibold mb-3">Follow Us</h3>
          <div class="flex gap-6 items-center">
            <!-- Facebook -->
            <a href="https://facebook.com/fabricspluscebu" target="_blank" aria-label="Facebook"
              class="text-gray-500 hover:text-red-800 transition">
              <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24" class="w-8 h-8">
                <path
                  d="M22 12a10 10 0 1 0-11.5 9.9v-7H8v-3h2.5V9.5a3.5 3.5 0 0 1 3.7-3.9c1 0 2 .2 2 .2v2.3H15a2 2 0 0 0-2.3 2V12H16l-.5 3h-2v7A10 10 0 0 0 22 12" />
              </svg>
            </a>

            <!-- Instagram -->
            <a href="https://instagram.com/fabricspluscebu" target="_blank" aria-label="Instagram"
              class="text-gray-500 hover:text-red-800 transition">
              <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24" class="w-8 h-8">
                <path
                  d="M7 2C4.2 2 2 4.2 2 7v10c0 2.8 2.2 5 5 5h10c2.8 0 5-2.2 5-5V7c0-2.8-2.2-5-5-5H7zm10 2c1.7 0 3 1.3 3 3v10c0 1.7-1.3 3-3 3H7c-1.7 0-3-1.3-3-3V7c0-1.7 1.3-3 3-3h10zm-5 3a5 5 0 1 0 0 10 5 5 0 0 0 0-10zm0 2a3 3 0 1 1 0 6 3 3 0 0 1 0-6zm4.8-.9a1.1 1.1 0 1 0 0-2.2 1.1 1.1 0 0 0 0 2.2z" />
              </svg>
            </a>

            <!-- YouTube -->
            <a href="https://youtube.com/@fabricspluscebu" target="_blank" aria-label="YouTube"
              class="text-gray-500 hover:text-red-800 transition">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-8 h-8">
                <path
                  d="M23.5 6.2a3 3 0 0 0-2.1-2.1C19.5 3.5 12 3.5 12 3.5s-7.5 0-9.4.6a3 3 0 0 0-2.1 2.1C0 8.1 0 12 0 12s0 3.9.5 5.8a3 3 0 0 0 2.1 2.1c1.9.6 9.4.6 9.4.6s7.5 0 9.4-.6a3 3 0 0 0 2.1-2.1c.5-1.9.5-5.8.5-5.8s0-3.9-.5-5.8zM9.8 15.5v-7l6.2 3.5-6.2 3.5z" />
              </svg>
            </a>


            <!-- TikTok -->
            <a href="https://tiktok.com/@fabricspluscebu" target="_blank" aria-label="TikTok"
              class="text-gray-500 hover:text-red-800 transition">
              <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24" class="w-8 h-8">
                <path
                  d="M12.7 2h3c.1 1 .5 2 1 ******* 1.6 1.6 2.7 1.8v3a6.4 6.4 0 0 1-3.7-1.2v7.6a6.4 6.4 0 1 1-6.4-6.4c.4 0 .7 0 1 .1v3a3.3 3.3 0 1 0 2.4 3.2V2z" />
              </svg>
            </a>
          </div>
        </div>

      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref, computed } from "vue";
import * as LucideIcons from "lucide-vue-next";
// import { MagnifyingGlassIcon } from '@heroicons/vue/24/outline'
const isOpen = ref(false)

const heroimages = [
  "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/improved/withTM1.png",
  "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/improved/withTM2.png",
  "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/improved/withTM3.png",
  "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/improved/withTM4.png",
];

const currentBg = ref(heroimages[0]);

const fadingOut = ref(false);
const fadingInFull = ref(false);
const fadingOutFull = ref(false);

let bgIndex = 0;

const sleep = (ms: number | undefined) => new Promise((res) => setTimeout(res, ms));

async function playLoop() {
  while (true) {
    // Full phrase stage FIRST
    fadingInFull.value = true;
    await sleep(200);
    fadingInFull.value = false;

    await sleep(3000); // visible for 5s

    fadingOutFull.value = true;
    await sleep(1000);
    fadingOutFull.value = false;

    await sleep(500);
  }
}


function cycleBackground() {
  setInterval(() => {
    bgIndex = (bgIndex + 1) % heroimages.length;
    currentBg.value = heroimages[bgIndex];
  }, 5000);
}

onMounted(() => {
  playLoop();
  cycleBackground();
});

const mode = ref<"brochure" | "presentation">("brochure");

const offers = [
  {
    icon: LucideIcons.Blinds,
    title: "Light & Ambience",
    desc: "Transform natural light into an element of design. From airy sheers that invite the sun to layered fabrics that set the mood, discover how window treatments shape your home’s atmosphere.",
    image: "https://images.unsplash.com/photo-*************-be6161a56a0c?q=80&w=800&auto=format&fit=crop",
  },
  {
    icon: LucideIcons.Columns2,
    title: "Modern Window Styling",
    desc: "Create interiors that balance elegance and control. Sleek blinds and smart layering give you the power to define privacy, shadow, and style—all in harmony with your space.",
    image: "https://images.unsplash.com/photo-*************-be6161a56a0c?q=80&w=800&auto=format&fit=crop",
  },
  {
    icon: LucideIcons.Bed,
    title: "Restful Living",
    desc: "A bedroom is more than a place to sleep—it’s a sanctuary. With thoughtful linens and textures, design a space that encourages calm mornings and truly restorative nights.",
    image: "https://images.unsplash.com/photo-*************-be6161a56a0c?q=80&w=800&auto=format&fit=crop",
  },
  {
    icon: LucideIcons.Sofa,
    title: "Layers of Comfort",
    desc: "Style comes alive in the details. Throws, bolsters, and bedskirts add dimension and warmth, offering endless ways to layer texture and personality into your home.",
    image: "https://images.unsplash.com/photo-*************-be6161a56a0c?q=80&w=800&auto=format&fit=crop",
  },
  {
    icon: LucideIcons.PenTool,
    title: "Custom-fit Solutions",
    desc: "Your vision, precisely tailored. We deliver made-to-measure designs that blend craftsmanship and creativity, ensuring a perfect fit for your home’s unique character.",
    image: "https://images.unsplash.com/photo-*************-be6161a56a0c?q=80&w=800&auto=format&fit=crop",
  },
  {
    icon: LucideIcons.SwatchBook,
    title: "Fabric Selection & Styling",
    desc: "Choose from a curated range of textures, tones, and finishes. Our guided styling helps you find fabrics that echo your personality while harmonizing with your interiors.",
    image: "https://images.unsplash.com/photo-*************-be6161a56a0c?q=80&w=800&auto=format&fit=crop",
  },
];



const differentiators = [
  {
    title: "Quality Materials",
    text: "We source only the best fabrics for durability and style.",
    image: "https://images.unsplash.com/photo-*************-be6161a56a0c?q=80&w=800&auto=format&fit=crop"
  },
  {
    title: "Affordable Prices",
    text: "Luxury designs that don’t break the bank.",
    image: "https://images.unsplash.com/photo-*************-be6161a56a0c?q=80&w=800&auto=format&fit=crop"
  },
  {
    title: "Trusted for 30+ Years",
    text: "A legacy of craftsmanship and customer trust.",
    image: "https://images.unsplash.com/photo-*************-be6161a56a0c?q=80&w=800&auto=format&fit=crop"
  }
];


const slides = [
  {
    title: "Fabrics Plus",
    body: "Trusted provider of premium curtains, blinds, and custom fabric solutions for homes, hotels, offices, and projects.",
  },
  { title: "What We Offer", cards: offers },
  {
    title: "What Makes Us Different",
    bullets: differentiators.map((d) => `${d.title} — ${d.text}`),
  },
  {
    title: "Our Promise",
    body: "We make every space beautiful, functional, and uniquely yours with expert guidance and precise customization.",
  },
  {
    title: "Let’s Work Together",
    body: `We don’t just decorate windows — we transform spaces. Call +63917-323-1366 or (06332)232-0740 or message us today for free consultation.`,
  },
];

const slideIndex = ref(0);
const currentSlide = computed(() => slides[slideIndex.value]);

function next() {
  slideIndex.value = (slideIndex.value + 1) % slides.length;
}
function prev() {
  slideIndex.value = (slideIndex.value - 1 + slides.length) % slides.length;
}
function go(i: number) {
  slideIndex.value = i;
}

function buttonClass(target: "brochure" | "presentation") {
  const active = mode.value === target;
  return active
    ? "bg-white shadow text-gray-900"
    : "text-gray-500 hover:text-gray-900";
}

function onKey(e: KeyboardEvent) {
  if (e.key.toLowerCase() === "p") {
    mode.value = mode.value === "brochure" ? "presentation" : "brochure";
  }
  if (mode.value === "presentation") {
    if (e.key === "ArrowRight") next();
    if (e.key === "ArrowLeft") prev();
  }
}

onMounted(() => window.addEventListener("keydown", onKey));
onBeforeUnmount(() => window.removeEventListener("keydown", onKey));
</script>

<style scoped>
/* Nav underline animation */
.nav-link {
  @apply relative transition-colors text-sm;
}

.nav-link::after {
  content: "";
  @apply absolute bottom-0 left-0 w-0 h-0.5 bg-red-700 transition-all;
}

.nav-link:hover::after {
  @apply w-full;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 1s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* segmented buttons used in hero */
.seg-btn {
  @apply inline-flex items-center gap-2 rounded-md px-4 py-2 text-sm font-semibold transition;
}

/* tweak seg-btn active appearance (buttonClass returns classes)
.seg-btn.bg-white {
  @apply bg-white;
} */

/* small responsive tweaks */
@media (min-width: 640px) {
  .seg-btn {
    @apply px-5 py-2.5;
  }
}
</style>
