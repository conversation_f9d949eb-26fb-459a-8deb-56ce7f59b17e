{"name": "nuxt-app", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@heroicons/vue": "^2.2.0", "@nuxtjs/tailwindcss": "^6.14.0", "lucide-vue-next": "^0.540.0", "moment": "^2.30.1", "nuxt": "^4.0.3", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.12", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.17"}}