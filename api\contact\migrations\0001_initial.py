# Generated by Django 5.0.2 on 2025-09-20 07:41

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ContactListModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('contact_id', models.Char<PERSON>ield(blank=True, default='', max_length=255, null=True)),
                ('firstname', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('lastname', models.Char<PERSON>ield(blank=True, default='', max_length=255, null=True)),
                ('contact_email', models.Char<PERSON><PERSON>(blank=True, default='', max_length=255, null=True)),
                ('contact_number', models.Char<PERSON>ield(blank=True, default='', max_length=255, null=True)),
                ('message', models.Char<PERSON>ield(blank=True, default='', max_length=500, null=True)),
                ('created_at', models.DateT<PERSON><PERSON><PERSON>(auto_now_add=True)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
    ]
