<template>
    <div class="min-h-screen text-[#2c2c2c] bg-gradient-to-br from-[#ddd2c2] via-[#ebeae4] to-[#f5e6ca]">
        <!-- Header -->
        <header 
            class="sticky top-0 z-40 backdrop-blur-sm bg-gradient-to-r from-[#ddd2c2]/90 via-[#ebeae4]/90 to-[#f5e6ca]/90">
            <div v-reveal="'up-md'" class="max-w-7xl mx-auto px-6 py-3 flex items-center justify-between text-[#2c2c2c] relative">
                <!-- Branding -->
                <NuxtLink to="/">
                    <div class="flex items-center gap-2 text-neutral-900">
                        <img src="/HDLogoFabricsPlusV2.png" class="w-10" />
                        <div>
                            <p class="text-lg font-bold">
                                Fabrics <span class="text-red-700">Plus</span>
                            </p>
                            <p class="text-xs text-gray-800">Philippines</p>
                        </div>
                    </div>
                </NuxtLink>

                <!-- Desktop Nav -->
                <nav class="hidden sm:flex gap-6 text-sm font-medium uppercase text-[#2c2c2c]/90">
                    <NuxtLink to="/about" class="nav-link hover:text-red-700">About</NuxtLink>
                    <NuxtLink to="/contact" class="nav-link hover:text-red-700">Contact</NuxtLink>
                    <NuxtLink to="/showroom/curtains" class="nav-link text-red-700">Showroom</NuxtLink>
                </nav>

                <!-- Call Button (Desktop) -->
                <a :href="`tel:${'0917-323-1366'}`"
                    class="hidden sm:inline-flex items-center gap-2 rounded-full px-4 py-2 border border-red-700 text-red-700 font-semibold hover:bg-red-700 hover:text-[#f5f5f2] transition">
                    📞 Call Us
                </a>

                <!-- Mobile Burger -->
                <div class="relative sm:hidden">
                    <button @click="isOpen = !isOpen" class="text-3xl text-red-800 font-bold">
                        ☰
                    </button>

                    <!-- Floating Dropdown -->
                    <div v-if="isOpen"
                        class="absolute right-0 mt-2 w-48 bg-[#f5e6ca] border border-red-700/30 rounded-lg shadow-lg py-3 space-y-2 text-[#2c2c2c] font-medium uppercase">
                        <NuxtLink to="/about" class="block px-4 py-2 hover:bg-red-700 hover:text-[#f5f5f2] rounded-md">
                            About
                        </NuxtLink>
                        <NuxtLink to="/contact"
                            class="block px-4 py-2 hover:bg-red-700 hover:text-[#f5f5f2] rounded-md">Contact
                        </NuxtLink>
                        <NuxtLink to="/comingsoon"
                            class="block px-4 py-2 hover:bg-red-700 hover:text-[#f5f5f2] rounded-md">Showroom
                        </NuxtLink>
                        <a :href="`tel:${'0917-323-1366'}`"
                            class="block px-4 py-2 hover:bg-red-700 hover:text-[#f5f5f2] rounded-md">📞 Call
                            Us</a>
                    </div>
                </div>
            </div>
        </header>

        <!-- Content with sticky category -->
        <div class="flex px-4 sm:px-8 gap-4">
            <!-- Sticky Sidebar Categories -->
            <aside v-reveal="'float-md'"  class="w-48 sm:w-60 md:w-72 sticky top-24 h-fit px-2 sm:px-4">
                <ul class="space-y-3 font-medium">
                    <li v-for="cat in categories" :key="cat.id">
                        <button @click="activeCategory = cat.id" :class="[
                            'block w-full text-left px-3 py-2 rounded-md transition',
                            activeCategory === cat.id
                                ? 'bg-red-700 text-white'
                                : 'hover:bg-neutral-700/80 hover:text-white'
                        ]">
                            {{ cat.title }}
                        </button>
                    </li>
                </ul>
            </aside>

            <!-- Masonry Grid -->
            <div class="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 flex-1">
                <div v-for="(colItems, colIdx) in columns" :key="colIdx" class="flex flex-col gap-4">
                    <div v-for="(item, idx) in colItems" :key="idx" v-reveal="'float-lg'" 
                        class="group cursor-pointer relative rounded-xl overflow-hidden shadow-md"
                        :style="{ height: `${item.height}px` }" @click="openLightbox(item)">
                        <img :src="item.src" :alt="item.title"
                            class="w-full h-full object-cover rounded-xl transition-transform duration-500 group-hover:scale-105" />
                        <div
                            class="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition flex items-center justify-center text-white text-sm p-2 text-center">
                            <span>{{ item.title }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <!-- Back to Top -->
        <button v-reveal="'up-sm'" v-show="showBackToTop" @click="scrollToTop"
            class="fixed bottom-8 right-8 bg-red-700 text-white rounded-full w-14 h-14 shadow-lg flex items-center justify-center hover:bg-red-800 transition-opacity duration-300"
            aria-label="Back to Top">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"
                class="w-7 h-7">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
            </svg>
        </button>

        <!-- Lightbox -->
        <div v-if="lightboxItem" class="fixed inset-0 bg-black/70 flex items-center justify-center z-50"
            @click.self="closeLightbox">
            <div class="bg-white max-w-2xl w-full rounded-lg p-6 relative shadow-xl">
                <button class="absolute top-3 right-5 text-2xl font-bold text-gray-600 hover:text-red-700"
                    @click="closeLightbox">
                    ✕
                </button>

                <h2 class="text-2xl font-bold mb-3">
                    {{ lightboxItem.title }}
                </h2>
                <p class="mb-4 text-gray-700">
                    {{ lightboxItem.description }}
                </p>

                <img v-if="lightboxItem.type === 'image'" :src="lightboxItem.src" class="w-full rounded-lg" />
                <video v-else controls class="w-full rounded-lg">
                    <source :src="lightboxItem.src" type="video/mp4" />
                </video>
            </div>
        </div>
        <!-- Footer -->
        <footer class="bg-neutral-900 text-[#f5f5f2] py-10 mt-10">
            <div class="max-w-7xl mx-auto px-6 grid grid-cols-1 sm:grid-cols-3 gap-8">
                <!-- Brand -->
                <div>
                    <img src="https://fpstorage.sgp1.cdn.digitaloceanspaces.com/HDLogoFabricsPlus.png" class="w-12 mb-3"
                        alt="Logo" />
                    <p class="font-bold">Cebu Las Aguadas Ventures Corp.</p>
                    <p class="text-sm">© 2025 Fabrics Plus. All Rights Reserved.</p>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="font-semibold mb-3">Quick Links</h3>
                    <ul class="space-y-2">
                        <li>
                            <NuxtLink to="/about" class="nav-link hover:text-red-500">About</NuxtLink>
                        </li>
                        <li>
                            <NuxtLink to="/contact" class="nav-link hover:text-red-500">Contact</NuxtLink>
                        </li>
                        <li>
                            <NuxtLink to="/comingsoon" class="nav-link hover:text-red-500">Showroom</NuxtLink>
                        </li>
                    </ul>
                </div>

                <!-- Socials -->
                <div>
                    <h3 class="font-semibold mb-3">Follow Us</h3>
                    <div class="flex gap-6 items-center">
                        <!-- Facebook -->
                        <a href="https://facebook.com/fabricspluscebu" target="_blank" aria-label="Facebook"
                            class="text-gray-500 hover:text-red-800 transition">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24"
                                class="w-8 h-8">
                                <path
                                    d="M22 12a10 10 0 1 0-11.5 9.9v-7H8v-3h2.5V9.5a3.5 3.5 0 0 1 3.7-3.9c1 0 2 .2 2 .2v2.3H15a2 2 0 0 0-2.3 2V12H16l-.5 3h-2v7A10 10 0 0 0 22 12" />
                            </svg>
                        </a>

                        <!-- Instagram -->
                        <a href="https://instagram.com/fabricspluscebu" target="_blank" aria-label="Instagram"
                            class="text-gray-500 hover:text-red-800 transition">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24"
                                class="w-8 h-8">
                                <path
                                    d="M7 2C4.2 2 2 4.2 2 7v10c0 2.8 2.2 5 5 5h10c2.8 0 5-2.2 5-5V7c0-2.8-2.2-5-5-5H7zm10 2c1.7 0 3 1.3 3 3v10c0 1.7-1.3 3-3 3H7c-1.7 0-3-1.3-3-3V7c0-1.7 1.3-3 3-3h10zm-5 3a5 5 0 1 0 0 10 5 5 0 0 0 0-10zm0 2a3 3 0 1 1 0 6 3 3 0 0 1 0-6zm4.8-.9a1.1 1.1 0 1 0 0-2.2 1.1 1.1 0 0 0 0 2.2z" />
                            </svg>
                        </a>

                        <!-- YouTube -->
                        <a href="https://youtube.com/@fabricspluscebu" target="_blank" aria-label="YouTube"
                            class="text-gray-500 hover:text-red-800 transition">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                                class="w-8 h-8">
                                <path
                                    d="M23.5 6.2a3 3 0 0 0-2.1-2.1C19.5 3.5 12 3.5 12 3.5s-7.5 0-9.4.6a3 3 0 0 0-2.1 2.1C0 8.1 0 12 0 12s0 3.9.5 5.8a3 3 0 0 0 2.1 2.1c1.9.6 9.4.6 9.4.6s7.5 0 9.4-.6a3 3 0 0 0 2.1-2.1c.5-1.9.5-5.8.5-5.8s0-3.9-.5-5.8zM9.8 15.5v-7l6.2 3.5-6.2 3.5z" />
                            </svg>
                        </a>


                        <!-- TikTok -->
                        <a href="https://tiktok.com/@fabricspluscebu" target="_blank" aria-label="TikTok"
                            class="text-gray-500 hover:text-red-800 transition">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24"
                                class="w-8 h-8">
                                <path
                                    d="M12.7 2h3c.1 1 .5 2 1 ******* 1.6 1.6 2.7 1.8v3a6.4 6.4 0 0 1-3.7-1.2v7.6a6.4 6.4 0 1 1-6.4-6.4c.4 0 .7 0 1 .1v3a3.3 3.3 0 1 0 2.4 3.2V2z" />
                            </svg>
                        </a>
                    </div>
                </div>

            </div>
        </footer>
    </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from "vue";
import { useRoute } from "vue-router"; // ✅ get route params

const route = useRoute();
const activeCategory = ref(route.params.id || "curtains");

watch(
    () => route.params.id,
    (newId) => {
        if (newId) activeCategory.value = newId;
    },
    { immediate: true }
);

const showBackToTop = ref(false);

//Showroom Categories
const categories = [
    {
        id: "curtains",
        title: "Curtains",
        description: "Elegant drapery designed to elevate your interiors with both style and privacy.",
        coverImage: "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/Curtains/IMG_9590.jpg",
        items: Array.from({ length: 20 }, (_, i) => ({
            title: `Curtain ${i + 1}`,
            description: "High-quality fabric curtain sample.",
            src: "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/improved/withTM1.png",
            type: "image",
            category: "Curtains",
        })),
    },

    {
        id: "swag-valances",
        title: "Swag Valances",
        description: "Decorate windows with elegance, adding a touch of charm to any room for a classic look.",
        coverImage: "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/Durawood%20Blinds/IMG_0827.JPG",
        items: Array.from({ length: 20 }, (_, i) => ({
            title: `Swag Valance ${i + 1}`,
            description: "Stylish swag valance design sample.",
            src: "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/Durawood%20Blinds/IMG_0827.JPG",
            type: "image",
            category: "Swag Valances",
        })),
    },

    {
        id: "blinds",
        title: "Combi, Roll Up & Durawood Blinds",
        description: "Tailored window covers, designed to block or filter light and provide privacy.",
        coverImage: "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/Bedskirts/IMG_3583.JPG",
        items: Array.from({ length: 20 }, (_, i) => ({
            title: `Blind ${i + 1}`,
            description: "Durable blinds sample item.",
            src: "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/Bedskirts/IMG_3583.JPG",
            type: "image",
            category: "Combi, Roll Up & Durawood Blinds",
        })),
    },

    {
        id: "bed-linens",
        title: "Bed Linens & Pillow Cases",
        description: "Soft, breathable fabrics for restful sleep—crafted for comfort and durability.",
        coverImage: "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/combi%20blinds/IMG-ddd2c653627d1c7fbcf4e67ab67a129b-V.jpg",
        items: Array.from({ length: 20 }, (_, i) => ({
            title: `Bed Linen ${i + 1}`,
            description: "Cozy and durable bed linen example.",
            src: "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/combi%20blinds/IMG-ddd2c653627d1c7fbcf4e67ab67a129b-V.jpg",
            type: "image",
            category: "Bed Linens & Pillow Cases",
        })),
    },

    {
        id: "pillows-bedskirts",
        title: "Throw, Bolster Pillows & Bedskirts",
        description: "Cozy finishing touches that bring warmth, charm, and character to your bedroom.",
        coverImage: "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/Throw%20pillow%20&%20Bolster%20pillows/IMG_3410.JPG",
        items: Array.from({ length: 20 }, (_, i) => ({
            title: `Pillow/Bedskirt ${i + 1}`,
            description: "Accent pillows and bedskirt design sample.",
            src: "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/Throw%20pillow%20&%20Bolster%20pillows/IMG_3410.JPG",
            type: "image",
            category: "Throw, Bolster Pillows & Bedskirts",
        })),
    },
];

// Reactive States
// Update when route param changes (e.g. navigating from Shop By Category)
watch(
    () => route.params.id,
    (newId) => {
        if (newId) activeCategory.value = newId;
    },
    { immediate: true }
);

// Pinterest Masonry Layout
const cols = 4; // number of columns
const columns = computed(() => {
    const cat = categories.find(c => c.id === activeCategory.value);
    if (!cat?.items) return [];

    // Initialize columns
    const colsArr = Array.from({ length: cols }, () => []);

    // Assign items to columns in column-first order
    cat.items.forEach((item, idx) => {
        const colIdx = idx % cols;
        item.height = Math.floor(Math.random() * 180) + 200; // random height
        colsArr[colIdx].push(item);
    });

    // Compute max column height
    const colHeights = colsArr.map(c => c.reduce((sum, it) => sum + it.height + 16, 0)); // 16 = gap
    const maxHeight = Math.max(...colHeights);

    // Adjust last item in each column to match maxHeight
    colsArr.forEach((col, i) => {
        const currentHeight = col.reduce((sum, it) => sum + it.height + 16, 0);
        const diff = maxHeight - currentHeight;
        if (diff > 0 && col.length) {
            col[col.length - 1].height += diff;
        }
    });

    return colsArr;
});


const lightboxItem = ref(null);

const openLightbox = (item) => {
    lightboxItem.value = item;
};
const closeLightbox = () => {
    lightboxItem.value = null;
};

const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
};

const handleScroll = () => {
    showBackToTop.value = window.scrollY > 200; // show after 200px scroll
};

onMounted(() => {
    window.addEventListener("scroll", handleScroll);
});

onUnmounted(() => {
    window.removeEventListener("scroll", handleScroll);
});
</script>

<style scoped>
.nav-link {
    @apply relative transition;
}

.nav-link::after {
    content: "";
    @apply absolute bottom-0 left-0 w-0 h-0.5 bg-red-600 transition-all;
}

.nav-link:hover::after {
    @apply w-full;
}

/* for pinterest-like layout  */
.grid-container {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 16px;
    grid-auto-rows: 10px;
    max-width: calc(100% - 20rem);
    /* reduce width so sidebar has space */
    margin-left: auto;
    margin-right: auto;
}

@media (min-width: 640px) {
    .grid-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 768px) {
    .grid-container {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1024px) {
    .grid-container {
        grid-template-columns: repeat(4, 1fr);
    }
}

.masonry-item img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 0.75rem;
}
</style>