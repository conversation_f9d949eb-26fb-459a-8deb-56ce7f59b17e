from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import generics, parsers, status
from .forms import ItemListForm
from .models import ItemListModel
from .serializers import ItemListSerializer, FileUploadSerializer
from django.template.loader import render_to_string
from django.core import mail
from django.utils.html import strip_tags
from django.forms.models import model_to_dict
import json
import datetime

class ItemListView(APIView):
    def get(self, request, format=None):
        obj = ItemListModel.objects.all()
        serializer = ItemListSerializer(obj, many=True)

        return Response(serializer.data)
 
class ItemCreateView(APIView):
    def post(self, request):
        form = ItemListForm(request.data)

        if form.is_valid():
            form_input = form.save(commit=False)
            form_input.save()

            return Response({'status':'created'})
        else:
            return Response({'status':'errors', 'errors': form.errors})
  
    def put(self, request, pk):
        form_input = ItemListModel.objects.get(pk=pk)
        form = ItemListForm(request.data, instance=form_input)
        if form.is_valid():
            form.save()
            return Response({'status': 'updated'})
        else:
            return Response({'status': 'errors', 'errors': form.errors}, status=status.HTTP_400_BAD_REQUEST)
    
    def delete(self, request, pk):
        form_input = ItemListModel.objects.get(pk=pk)
        form_input.delete()

        return Response({'status': 'deleted'})

class ItemDetailView(APIView):
    def get(self, request, pk, format=None):
        obj = ItemListModel.objects.get(pk=pk)
        serializer = ItemListSerializer(obj)

        return Response(serializer.data)

class FileUploadView(generics.GenericAPIView):
    parser_classes = (parsers.MultiPartParser,)
    serializer_class = FileUploadSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            uploaded_files = serializer.save()  # Get the list of instances
            return Response(serializer.to_representation(uploaded_files), status=status.HTTP_201_CREATED) # Serialize the list
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)