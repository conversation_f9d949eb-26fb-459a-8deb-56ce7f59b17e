# Generated by Django 5.0.2 on 2025-09-23 11:18

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ContactListModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item_id', models.Char<PERSON>ield(blank=True, default='', max_length=255, null=True)),
                ('title', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('description', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('thumbnail', models.Char<PERSON>ield(blank=True, default='', max_length=255, null=True)),
                ('banner_image', models.Char<PERSON>ield(blank=True, default='', max_length=255, null=True)),
                ('category_name', models.Char<PERSON>ield(blank=True, default='', max_length=500, null=True)),
                ('created_at', models.DateT<PERSON><PERSON><PERSON>(auto_now_add=True)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
    ]
