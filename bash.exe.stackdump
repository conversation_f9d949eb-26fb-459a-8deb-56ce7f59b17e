Stack trace:
Frame         Function      Args
0007FFFF9F10  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8E10) msys-2.0.dll+0x2118E
0007FFFF9F10  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFA1E8) msys-2.0.dll+0x69BA
0007FFFF9F10  0002100469F2 (00021028DF99, 0007FFFF9DC8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9F10  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9F10  00021006A545 (0007FFFF9F20, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFA1F0  00021006B9A5 (0007FFFF9F20, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFFD8680000 ntdll.dll
7FFFD6FE0000 KERNEL32.DLL
7FFFD5D50000 KERNELBASE.dll
7FFFD6DD0000 USER32.dll
7FFFD6150000 win32u.dll
7FFFD7D20000 GDI32.dll
7FFFD5AC0000 gdi32full.dll
7FFFD5980000 msvcp_win.dll
7FFFD6300000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFFD7110000 advapi32.dll
7FFFD6980000 msvcrt.dll
7FFFD8440000 sechost.dll
7FFFD7BF0000 RPCRT4.dll
7FFFD4E30000 CRYPTBASE.DLL
7FFFD5CB0000 bcryptPrimitives.dll
7FFFD6FA0000 IMM32.DLL
