# Generated by Django 5.0.2 on 2025-09-23 11:29

import django_jsonform.models.fields
import storages.backends.s3
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('item', '0002_rename_contactlistmodel_itemlistmodel'),
    ]

    operations = [
        migrations.CreateModel(
            name='FileUploadModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(storage=storages.backends.s3.S3Storage(), upload_to='fabricsplus/items/uploads/')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.AlterField(
            model_name='itemlistmodel',
            name='thumbnail',
            field=django_jsonform.models.fields.J<PERSON>NField(null=True),
        ),
    ]
