<template>
  <div
    class="min-h-screen text-[#2c2c2c] bg-gradient-to-br from-[#ddd2c2] via-[#ebeae4] to-[#f5e6ca]"
  >
    <!-- Header -->
    <header
      class="sticky top-0 z-40 backdrop-blur-sm bg-gradient-to-r from-[#ddd2c2]/90 via-[#ebeae4]/90 to-[#f5e6ca]/90"
    >
      <div
        class="max-w-7xl mx-auto px-6 py-3 flex items-center justify-between text-[#2c2c2c] relative"
      >
        <!-- Branding -->
        <NuxtLink to="/">
          <div class="flex items-center gap-2 text-neutral-900">
            <img src="/HDLogoFabricsPlusV2.png" class="w-10" />
            <div>
              <p class="text-lg font-bold">
                Fabrics <span class="text-red-700">Plus</span>
              </p>
              <p class="text-xs text-gray-800">Philippines</p>
            </div>
          </div>
        </NuxtLink>

        <!-- Desktop Nav -->
        <nav
          class="hidden sm:flex gap-6 text-sm font-medium uppercase text-[#2c2c2c]/90"
        >
          <NuxtLink to="/about" class="nav-link hover:text-red-700"
            >About</NuxtLink
          >
          <NuxtLink to="/contact" class="nav-link hover:text-red-700"
            >Contact</NuxtLink
          >
          <NuxtLink to="/showroom/curtains" class="nav-link hover:text-red-700"
            >Showroom</NuxtLink
          >
        </nav>

        <!-- Call Button (Desktop) -->
        <a
          :href="`tel:${'0917-323-1366'}`"
          class="hidden sm:inline-flex items-center gap-2 rounded-full px-4 py-2 border border-red-700 text-red-700 font-semibold hover:bg-red-700 hover:text-[#f5f5f2] transition"
        >
          📞 Call Us
        </a>

        <!-- Mobile Burger -->
        <div class="relative sm:hidden">
          <button
            @click="isOpen = !isOpen"
            class="text-3xl text-red-800 font-bold"
          >
            ☰
          </button>

          <!-- Floating Dropdown -->
          <div
            v-if="isOpen"
            class="absolute right-0 mt-2 w-48 bg-[#f5e6ca] border border-red-700/30 rounded-lg shadow-lg py-3 space-y-2 text-[#2c2c2c] font-medium uppercase"
          >
            <NuxtLink
              to="/about"
              class="block px-4 py-2 hover:bg-red-700 hover:text-[#f5f5f2] rounded-md"
              >About
            </NuxtLink>
            <NuxtLink
              to="/contact"
              class="block px-4 py-2 hover:bg-red-700 hover:text-[#f5f5f2] rounded-md"
              >Contact
            </NuxtLink>
            <NuxtLink
              to="/showroom/curtains"
              class="block px-4 py-2 hover:bg-red-700 hover:text-[#f5f5f2] rounded-md"
              >Showroom
            </NuxtLink>
            <a
              :href="`tel:${'0917-323-1366'}`"
              class="block px-4 py-2 hover:bg-red-700 hover:text-[#f5f5f2] rounded-md"
              >📞 Call Us</a
            >
          </div>
        </div>
      </div>
    </header>

    <!-- Hero Section -->
    <section
      v-reveal="'up-md'"
      class="relative bg-cover bg-center h-[80vh] flex items-center justify-center transition-all duration-1000"
      :style="{ backgroundImage: `url(${currentBg})` }"
    >
      <div class="absolute inset-0 bg-black/40"></div>

      <div class="relative text-center text-[#f5f5f2] px-6 max-w-6xl">
        <!-- Animated Headline -->
        <div class="h-20 sm:h-24 mb-6 flex items-center justify-center">
          <!-- <h1 v-if="!fullText" class="text-6xl sm:text-7xl font-bold transition-opacity duration-800"
            :class="{ 'opacity-0': fadingOut, 'opacity-100': !fadingOut }">
            {{ displayedText }}
          </h1>

          <h1 v-else class="text-6xl sm:text-7xl font-bold absolute transition-opacity duration-1000" :class="{
            'opacity-0': fadingInFull || fadingOutFull,
            'opacity-100': !fadingInFull && !fadingOutFull
          }">
            Style. Comfort. <span class="text-red-600">Perfect</span> Fit.
          </h1> -->

          <h1 class="text-6xl sm:text-7xl font-bold absolute">
            Style. Comfort. <span class="text-red-600">Perfect</span> Fit.
          </h1>
        </div>

        <!-- Subtitle -->
        <p class="max-w-4xl mx-auto text-lg sm:text-xl mb-8">
          Premium curtains, blinds, and fabrics tailored for homes, hotels, and
          businesses in the Philippines.
        </p>

        <!-- CTA -->
        <div class="flex justify-center gap-4">
          <NuxtLink
            to="/showroom/curtains"
            class="px-6 py-3 bg-red-700 hover:bg-red-800 rounded-full text-[#f5f5f2] font-semibold transition"
          >
            Visit Showroom
          </NuxtLink>
        </div>
      </div>
    </section>

    <!-- Infinite Interior Design Slider -->
    <section
      v-reveal="'float-md'"
      class="relative w-full overflow-hidden bg-gray-50 py-2"
    >
      <div
        class="flex"
        :style="{
          transform: `translateX(-${offset}px)`,
          transition: 'none',
        }"
      >
        <div
          v-for="(img, idx) in trackImages"
          :key="idx"
          class="flex-shrink-0 px-[2px]"
          :style="{ width: slideWidth + 'px' }"
        >
          <img
            :src="img"
            class="w-full h-full object-cover rounded-xs shadow-md transition"
          />
        </div>
      </div>
    </section>

    <!-- Shop by Category -->
    <section v-reveal="'up-sm'" class="px-4 sm:px-6 pt-12 pb-16">
      <h3
        class="text-center text-lg font-bold tracking-widest text-red-700 mb-8"
      >
        Shop By Category
      </h3>

      <!-- Flex wrapper -->
      <div class="flex flex-wrap gap-6">
        <NuxtLink
          v-for="(cat, i) in shopByCategory"
          :key="i"
          :to="`/detail/${cat.id}`"
          class="flex-1 min-w-full <!-- 1 col on phones --> sm:min-w-[210px] md:min-w-[30%] <!-- ~3 per row --> lg:min-w-[18%] <!-- 5 per row --> relative group overflow-hidden rounded-xl border cursor-pointer"
        >
          <!-- Image -->
          <img
            :src="cat.banner_image"
            :alt="cat.title"
            class="w-full h-28 sm:h-40 md:h-48 object-cover transform transition-transform duration-500 group-hover:scale-110"
          />

          <!-- Overlay -->
          <div
            class="absolute inset-x-0 bottom-0 bg-black/50 transition-all duration-500 group-hover:inset-0 group-hover:bg-black/70 flex flex-col justify-end"
          >
            <div class="p-10 sm:p-4">
              <!-- Title -->
              <h4
                class="font-semibold text-sm sm:text-base text-[#f5f5f2] transition-transform duration-500 group-hover:translate-y-[-20%]"
              >
                {{ cat.title }}
              </h4>

              <!-- Description with delayed reveal -->
              <p
                class="text-xs sm:text-sm text-[#f5f5f2]/90 opacity-0 max-h-0 overflow-hidden transition-all duration-500 delay-300 group-hover:opacity-100 group-hover:max-h-40 group-hover:mt-6"
              >
                {{ cat.description }}
              </p>
            </div>
          </div>
        </NuxtLink>
      </div>
    </section>

    <!-- Features -->
    <section v-reveal="'up-md'" class="px-6 py-10">
      <h2 class="text-3xl font-bold text-center mb-12 text-[#2c2c2c]">
        Why Choose Us
      </h2>
      <div class="grid sm:grid-cols-3 gap-12 max-w-6xl mx-auto">
        <div
          v-for="(f, i) in features"
          :key="i"
          v-reveal="i % 2 === 0 ? 'float-sm' : 'float-md'"
          class="p-8 rounded-2xl border text-center hover:border-red-600 transition group cursor-pointer"
        >
          <!-- Dynamic Icon -->
          <component
            :is="LucideIcons[f.icon]"
            class="w-16 h-16 text-red-600 mx-auto mb-6 transition-transform duration-300 group-hover:scale-125"
          />

          <!-- Title -->
          <h3
            class="text-xl font-semibold mb-3 text-[#2c2c2c] transition-all duration-300 group-hover:text-red-600 group-hover:scale-125"
          >
            {{ f.title }}
          </h3>

          <!-- Description -->
          <p class="text-sm text-[#2c2c2c]/70">{{ f.desc }}</p>
        </div>
      </div>
    </section>

    <!-- Trusted Clients -->
    <section
      v-reveal="'float-lg'"
      class="px-6 py-16 bg-gradient-to-br from-gray-100 via-neutral-300 to-gray-300"
    >
      <h2 class="text-3xl font-semibold text-center mb-10 text-[#2c2c2c]">
        Trusted By
      </h2>

      <div
        class="max-w-7xl mx-auto flex flex-wrap gap-4 justify-center items-center"
      >
        <div
          v-for="(brand, idx) in trusted"
          :key="idx"
          class="flex-1 flex justify-center group"
        >
          <a
            :href="brand.url"
            target="_blank"
            rel="noopener noreferrer"
            class="block"
          >
            <img
              :src="brand.logo"
              :alt="brand.name"
              class="h-36 filter grayscale group-hover:grayscale-0 transition duration-500 group-hover:scale-125 group-hover:drop-shadow-lg"
            />
          </a>
        </div>
      </div>
    </section>

    <!-- CTA -->
    <section v-reveal="'up-lg'" class="px-6 py-20 text-[#2c2c2c] text-center">
      <h2 class="text-3xl sm:text-4xl font-bold mb-6">
        Let’s Transform Your Space
      </h2>
      <p class="max-w-2xl mx-auto mb-8 text-[#2c2c2c]/80">
        Explore our premium fabrics and custom solutions for your next project.
      </p>
      <div class="flex flex-wrap justify-center gap-4">
        <!-- Call Button -->
        <a
          href="tel:+639123456789"
          class="flex items-center gap-2 px-6 py-3 bg-red-700 hover:bg-transparent hover:border hover:border-red-800 hover:text-red-800 rounded-full text-[#f5f5f2] shadow transition font-semibold"
        >
          <!-- Phone Icon -->
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            class="w-5 h-5"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M2 5.5C2 4.67 2.67 4 3.5 4h2c.55 0 1 .45 1 1 0 1.38.56 2.63 1.46 3.54l1.13 1.13c.2.2.2.51 0 .71l-1.6 1.6c1.18 2.54 3.08 4.44 5.62 5.62l1.6-1.6c.2-.2.51-.2.71 0l1.13 1.13c.91.91 2.16 1.46 3.54 1.46.55 0 1 .45 1 1v2c0 .83-.67 1.5-1.5 1.5C8.28 22 2 15.72 2 7.5V5.5z"
            />
          </svg>
          Call or Message Us Today
        </a>

        <!-- Showroom Button -->
        <NuxtLink
          to="/comingsoon"
          class="flex items-center gap-2 px-6 py-3 border border-neutral-800 text-[#2c2c2c] rounded-full hover:bg-neutral-800 hover:text-[#f5f5f2] transition font-semibold"
        >
          <!-- Store / Building Icon -->
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            class="w-5 h-5"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M3 9.75V21h6V14h6v7h6V9.75M3 9.75L12 3l9 6.75M9 21V14h6v7"
            />
          </svg>
          Visit Showroom
        </NuxtLink>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-neutral-900 text-[#f5f5f2] py-10 mt-10">
      <div class="max-w-7xl mx-auto px-6 grid grid-cols-1 sm:grid-cols-3 gap-8">
        <!-- Brand -->
        <div>
          <img
            src="https://fpstorage.sgp1.cdn.digitaloceanspaces.com/HDLogoFabricsPlus.png"
            class="w-12 mb-3"
            alt="Logo"
          />
          <p class="font-bold">Cebu Las Aguadas Ventures Corp.</p>
          <p class="text-sm">© 2025 Fabrics Plus. All Rights Reserved.</p>
        </div>

        <!-- Quick Links -->
        <div>
          <h3 class="font-semibold mb-3">Quick Links</h3>
          <ul class="space-y-2">
            <li>
              <NuxtLink to="/about" class="nav-link hover:text-red-500"
                >About</NuxtLink
              >
            </li>
            <li>
              <NuxtLink to="/contact" class="nav-link hover:text-red-500"
                >Contact</NuxtLink
              >
            </li>
            <li>
              <NuxtLink to="/comingsoon" class="nav-link hover:text-red-500"
                >Showroom</NuxtLink
              >
            </li>
          </ul>
        </div>

        <!-- Socials -->
        <div>
          <h3 class="font-semibold mb-3">Follow Us</h3>
          <div class="flex gap-6 items-center">
            <!-- Facebook -->
            <a
              href="https://facebook.com/fabricspluscebu"
              target="_blank"
              aria-label="Facebook"
              class="text-gray-500 hover:text-red-800 transition"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="currentColor"
                viewBox="0 0 24 24"
                class="w-8 h-8"
              >
                <path
                  d="M22 12a10 10 0 1 0-11.5 9.9v-7H8v-3h2.5V9.5a3.5 3.5 0 0 1 3.7-3.9c1 0 2 .2 2 .2v2.3H15a2 2 0 0 0-2.3 2V12H16l-.5 3h-2v7A10 10 0 0 0 22 12"
                />
              </svg>
            </a>

            <!-- Instagram -->
            <a
              href="https://instagram.com/fabricspluscebu"
              target="_blank"
              aria-label="Instagram"
              class="text-gray-500 hover:text-red-800 transition"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="currentColor"
                viewBox="0 0 24 24"
                class="w-8 h-8"
              >
                <path
                  d="M7 2C4.2 2 2 4.2 2 7v10c0 2.8 2.2 5 5 5h10c2.8 0 5-2.2 5-5V7c0-2.8-2.2-5-5-5H7zm10 2c1.7 0 3 1.3 3 3v10c0 1.7-1.3 3-3 3H7c-1.7 0-3-1.3-3-3V7c0-1.7 1.3-3 3-3h10zm-5 3a5 5 0 1 0 0 10 5 5 0 0 0 0-10zm0 2a3 3 0 1 1 0 6 3 3 0 0 1 0-6zm4.8-.9a1.1 1.1 0 1 0 0-2.2 1.1 1.1 0 0 0 0 2.2z"
                />
              </svg>
            </a>

            <!-- YouTube -->
            <a
              href="https://youtube.com/@fabricspluscebu"
              target="_blank"
              aria-label="YouTube"
              class="text-gray-500 hover:text-red-800 transition"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
                class="w-8 h-8"
              >
                <path
                  d="M23.5 6.2a3 3 0 0 0-2.1-2.1C19.5 3.5 12 3.5 12 3.5s-7.5 0-9.4.6a3 3 0 0 0-2.1 2.1C0 8.1 0 12 0 12s0 3.9.5 5.8a3 3 0 0 0 2.1 2.1c1.9.6 9.4.6 9.4.6s7.5 0 9.4-.6a3 3 0 0 0 2.1-2.1c.5-1.9.5-5.8.5-5.8s0-3.9-.5-5.8zM9.8 15.5v-7l6.2 3.5-6.2 3.5z"
                />
              </svg>
            </a>

            <!-- TikTok -->
            <a
              href="https://tiktok.com/@fabricspluscebu"
              target="_blank"
              aria-label="TikTok"
              class="text-gray-500 hover:text-red-800 transition"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="currentColor"
                viewBox="0 0 24 24"
                class="w-8 h-8"
              >
                <path
                  d="M12.7 2h3c.1 1 .5 2 1 ******* 1.6 1.6 2.7 1.8v3a6.4 6.4 0 0 1-3.7-1.2v7.6a6.4 6.4 0 1 1-6.4-6.4c.4 0 .7 0 1 .1v3a3.3 3.3 0 1 0 2.4 3.2V2z"
                />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import * as LucideIcons from "lucide-vue-next";
// import { MagnifyingGlassIcon } from '@heroicons/vue/24/outline'

const heroimages = [
  "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/improved/withTM1.png",
  "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/improved/withTM2.png",
  "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/improved/withTM3.png",
  "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/improved/withTM4.png",
];

const phrases = ["Style.", "Comfort.", "The Perfect Fit."];

const currentBg = ref(heroimages[0]);
const displayedText = ref("");
const fullText = ref(false);

const fadingOut = ref(false);
const fadingInFull = ref(false);
const fadingOutFull = ref(false);

let bgIndex = 0;

const sleep = (ms) => new Promise((res) => setTimeout(res, ms));

async function typeText(text) {
  displayedText.value = "";
  for (let i = 0; i < text.length; i++) {
    displayedText.value += text[i];
    await sleep(100); // typing speed
  }
  await sleep(800); // pause before fade out
  fadingOut.value = true;
  await sleep(800); // fade duration
  displayedText.value = "";
  fadingOut.value = false;
}

async function playLoop() {
  while (true) {
    // Full phrase stage FIRST
    fullText.value = true;
    fadingInFull.value = true;
    await sleep(200);
    fadingInFull.value = false;

    await sleep(3000); // visible for 5s

    fadingOutFull.value = true;
    await sleep(1000);
    fullText.value = false;
    fadingOutFull.value = false;

    await sleep(500);

    // Then typing stage
    for (const p of phrases) {
      await typeText(p);
      await sleep(400);
    }
  }
}

function cycleBackground() {
  setInterval(() => {
    bgIndex = (bgIndex + 1) % heroimages.length;
    currentBg.value = heroimages[bgIndex];
  }, 5000);
}

onMounted(() => {
  playLoop();
  cycleBackground();
});

//Slider Images
const images = [
  "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/improved/withTM1.png",
  "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/improved/withTM2.png",
  "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/improved/withTM3.png",
  "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/improved/withTM4.png",
];

const trackImages = Array(5).fill(images).flat();

const offset = ref(0);
let slideWidth = 250; // default (mobile)
const speed = 0.3;
let animationFrame;

// Adjust slide width based on screen size
const updateSlideWidth = () => {
  if (window.innerWidth < 640) slideWidth = 250;
  else if (window.innerWidth < 768) slideWidth = 350;
  else slideWidth = 500;
};

const animate = () => {
  offset.value += speed;
  if (offset.value >= images.length * slideWidth) {
    offset.value = 0;
  }
  animationFrame = requestAnimationFrame(animate);
};

onMounted(() => {
  updateSlideWidth();
  window.addEventListener("resize", updateSlideWidth);
  animationFrame = requestAnimationFrame(animate);
});

// Shop Categories
// const shopByCategory = [
//   {
//     id: "curtains",
//     title: "Curtains",
//     desc: "Elegant drapery designed to elevate your interiors with both style and privacy.",
//     image:
//       "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/Curtains/IMG_9590.jpg",
//   },
//   {
//     id: "swag-valances",
//     title: "Swag Valances",
//     desc: "Decorate windows with elegance adding a touch of charm to any room for a classic look.",
//     image:
//       "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/Curtains/Swag%20Valances/IMG-c9e36db01ca7382b0ead32c8b65a16d0-V.jpg",
//   },
//   {
//     id: "blinds",
//     title: "Combi, Roll Up, and Durawood Blinds",
//     desc: " Tailored window coverings designed to block or filter light and provide privacy.",
//     image:
//       "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/combi%20blinds/IMG-ddd2c653627d1c7fbcf4e67ab67a129b-V.jpg",
//   },
//   {
//     id: "bed-linens",
//     title: "Bed Linens and Pillow Cases",
//     desc: "Soft, breathable fabrics for restful sleep—crafted for comfort and durability.",
//     image:
//       "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/Throw%20pillow%20&%20Bolster%20pillows/IMG_8995.JPG",
//   },
//   {
//     id: "pillows-bedskirts",
//     title: "Throw, Bolster Pillows and Bedskirts",
//     desc: "Cozy finishing touches that bring warmth, charm, and character to your bedroom.",
//     image:
//       "https://fpstorage.sgp1.cdn.digitaloceanspaces.com/Fabric%20Plus%20Webpage/Throw%20pillow%20&%20Bolster%20pillows/IMG_3410.JPG",
//   },
// ];

const serverIP = "https://api.fabricspluscurtains.com";

const shopByCategory = ref([]);
const isLoading = ref(false);

onMounted(() => {
  fetchShopByCategory();
});

const fetchShopByCategory = async () => {
  isLoading.value = true;
  try {
    const response = await $fetch(serverIP + "/api/item/list").catch(
      (error) => {
        console.error("Fetch error:", error);
        return [];
      }
    );

    // Ensure shopByCategory is always an array
    shopByCategory.value = Array.isArray(response) ? response : [];

    console.log("Fetched items:", shopByCategory.value);
    // Reset search state
    // originalShopByCategory.value = [];
    // searchQuery.value = "";
  } catch (error) {
    console.error("Error fetching list items:", error);
    shopByCategory.value = []; // fallback
  } finally {
    isLoading.value = false;
  }
};

const features = [
  {
    title: "Custom Fit",
    desc: "Every window, every corner, every space is unique. Our solutions are tailored to match your exact dimensions, style preferences, and lifestyle needs.",
    icon: "Ruler",
  },
  {
    title: "Craftsmanship",
    desc: "Handcrafted with precision and care, our products combine timeless artistry with durable materials—ensuring elegance that lasts for years.",
    icon: "Hammer",
  },
  {
    title: "Nationwide Delivery",
    desc: "From our workshop straight to your doorstep—wherever you are in the Philippines, we make sure beauty and quality reach you on time.",
    icon: "Truck",
  },
];

const trusted = [
  {
    name: "Radisson Blu",
    logo: "/logos/radisson_blu.svg",
    url: "https://www.radissonhotels.com/en-us/brand/radisson-blu",
  },
  {
    name: "Maayo Hotels",
    logo: "/logos/maayo_hotels.svg",
    url: "https://www.maayoleisuregroup.com/maayo-hotel-1",
  },
  {
    name: "JPark Resort Hotel",
    logo: "/logos/jpark.svg",
    url: "https://www.jparkislandresort.com/",
  },
  {
    name: "Bai Hotel",
    logo: "/logos/bai_hotel.svg",
    url: "https://www.baihotels.com/",
  },
  {
    name: "Ayala The City Flats",
    logo: "/logos/cityflats.svg",
    url: "https://ayalaland.com/locations/cebu-city",
  },
];
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 1s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.nav-link {
  @apply relative transition;
}

.nav-link::after {
  content: "";
  @apply absolute bottom-0 left-0 w-0 h-0.5 bg-red-600 transition-all;
}

.nav-link:hover::after {
  @apply w-full;
}

/* Optional marquee fallback */
@keyframes marquee {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-50%);
  }
}

.animate-marquee {
  display: flex;
  width: max-content;
  animation: marquee 40s linear infinite;
}

@keyframes scroll {
  0% {
    transform: translateX(0%);
  }

  100% {
    transform: translateX(-50%);
  }
}

.animate-scroll {
  display: flex;
  animation: scroll 20s linear infinite;
  width: max-content;
}
</style>
